# World MD Configuration File - English Version
# This file contains all configuration options for the World MD tool
# Optimized for English documents and Western typography standards

# Font Configuration
# Sets fonts for different parts of the document
fonts:
  default: Calibri     # Main body font - standard for English documents
  code: Consolas             # Code block font - clear monospace font for code
  headings: Calibri          # Heading font - modern, readable sans-serif

# Size Configuration (units: points/pt)
# Controls font sizes for various document elements
sizes:
  default: 11               # Main body font size (standard for business documents)
  code: 9                   # Code block font size (slightly smaller for readability)
  heading1: 18              # Level 1 heading size
  heading2: 16              # Level 2 heading size
  heading3: 14              # Level 3 heading size
  heading4: 12              # Level 4 heading size
  heading5: 11              # Level 5 heading size
  heading6: 10              # Level 6 heading size

# Color Configuration (using hexadecimal color codes)
# Sets text colors for various document elements
colors:
  default: '#000000'        # Main body text color (black)
  headings: '#2F4F4F'       # Heading color (dark slate gray for better hierarchy)
  code: '#8B0000'           # Code color (dark red for distinction)
  link: '#0563C1'           # Link color (standard blue)

# Paragraph Configuration
# Controls paragraph formatting and spacing
paragraph:
  line_spacing: 1.15        # Line spacing multiplier (1.15 is standard for English)
  space_before: 0           # Space before paragraph (points)
  space_after: 6            # Space after paragraph (points)
  first_line_indent: 0      # First line indent (0 for modern English documents)

# Language Configuration
# Language-specific processing options (disabled for English)
chinese:
  convert_to_traditional: false   # Disable Chinese conversion for English docs
  punctuation_spacing: false      # Disable Chinese punctuation processing
  auto_spacing: false             # Disable Chinese-English auto spacing

# Table Styles Configuration
# HTML table styling (for intermediate HTML files)
table_styles:
  even_row_color: '#f8f9fa'       # Even row background (light gray)
  odd_row_color: '#ffffff'        # Odd row background (white)
  header_bg_color: '#e9ecef'      # Header background (medium gray)
  border_color: '#dee2e6'         # Border color (light gray)
  cell_height: 1.2em              # Cell height (slightly larger for English text)
  table_width: 100%               # Table width (percentage)

# Enhanced Table Styles Configuration (Word Document)
# Controls specific table styling and formatting in Word documents
enhanced_table_styles:
  style: Table Grid               # Word table style name
  width: 16.0                     # Table width (centimeters)
  border: true                    # Show borders
  border_size: 1                  # Border thickness (points)
  border_color: '#dee2e6'         # Border color (light gray)
  header_bg_color: '#f8f9fa'      # Header background color
  even_row_color: '#ffffff'       # Even row background color
  text_align: left                # Horizontal text alignment (left for English)
  vertical_align: center          # Vertical text alignment
  cell_padding: 4                 # Cell padding (points) - more space for English
  cell_height: 1.0                # Default cell height (centimeters)
  autofit: true                   # Auto-fit content width (better for English)
  first_row_as_header: true       # Treat first row as header
  keep_header_visible: true       # Keep header visible when paginating
  row_height:                     # Detailed row height configuration
    default: 1.0                  # Default row height (centimeters)
    header: 1.2                   # Header row height (centimeters)
    min: 0.6                      # Minimum allowed row height
    max: 5.0                      # Maximum allowed row height
    auto_adjust: true             # Auto-adjust row height based on content

# Markdown Parsing Configuration
# Controls Markdown parser behavior and supported extensions
markdown:
  extensions:                     # List of enabled Markdown extensions
  - tables                        # Table support
  - fenced_code                   # Fenced code blocks (using ``` markers)
  - codehilite                    # Code highlighting
  - toc                           # Table of contents generation (useful for English docs)
  - footnotes                     # Footnote support
  - nl2br                         # Convert line breaks to <br> tags
  extension_configs:              # Specific extension configurations
    codehilite:                   # Code highlighting configuration
      linenums: false             # Show line numbers
      use_pygments: true          # Use Pygments for syntax highlighting

# Document Configuration
# Word document page settings and global options
document:
  page_size: Letter               # Page size (Letter is standard in US/Canada)
  margin_top: 1.0                 # Top margin (inches) - US standard
  margin_bottom: 1.0              # Bottom margin (inches)
  margin_left: 1.0                # Left margin (inches)
  margin_right: 1.0               # Right margin (inches)
  header: ''                      # Header content (empty = no header)
  footer: ''                      # Footer content (empty = no footer)
  generate_toc: false             # Generate table of contents (useful for English docs)

# Debug Configuration
# Controls runtime logging and debug information
debug:
  enabled: false                  # Enable debug mode
  log_level: INFO                 # Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
  log_to_file: false              # Write logs to file
  log_file: conversion.log        # Log file path
  print_html_structure: false     # Print HTML structure (for debugging)
  verbose_element_info: false     # Output detailed element information
  timing: true                    # Record processing time statistics
